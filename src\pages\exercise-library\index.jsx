import React, { Children, useEffect, useReducer, useRef, useState } from 'react';
import Exerciseimg3 from '@assets/images/exercise-library/exercise3.png';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { DndProvider } from 'react-dnd';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import useCalculatePrescriptionWidth from '@/hooks/exercises-library/useCalculatePrescriptionWidth';
import SourceItem from '@/components/exerciseLibrary/sourceExerciseLibraryItem';
import SourceItemTemplate from '@/components/exerciseLibrary/sourceTemplateCard';
import PrescriptionCard from '@/components/exerciseLibrary/prescriptionCard';
import Filter from '@/components/exerciseLibrary/filter';
import TabsPage from '@/components/exerciseLibrary/tabs';
import { InitialState } from '@/components/exerciseLibrary/prescriptionForm/module';
import ExerciseDetailsModel from '@/components/exerciseLibrary/exerciseDetailsModel';
import PrimaryButton from '@/components/shared/primaryButton';
import SecondaryButton from '@/components/shared/secondaryButton';
import tw from 'twin.macro';
import {
  exerciseLibraryActions,
  exerciseLibraryReducer,
  initialValues,
} from '@/reducers/exercise-library';
import { showError, showSuccess } from '@/libs/react.toastify';
import EditTemplateModel from '@/components/exerciseLibrary/editTemplateModel';
import SaveTemplateModal from '@/components/exerciseLibrary/saveTemplateModal';
import { useExerciseLibrary } from '@/zustand/exercise-library';
import { prescriptionMode } from '@/constants/constants';
import { schemaPrescriptionForm } from '@/components/exerciseLibrary/prescriptionForm/schema';
import {
  getPrescriptionById,
  mapPrescriptionToExerciseLibraryView,
} from '@/mock/prescriptions-data';
import PrescriptionModal, { ModalViews } from '@/components/exerciseLibrary/prescriptionModal';
import cancelSubscriptionModal from '@assets/svgs/settings/solar_danger-triangle-outline.svg';
import PreSessionMessages from '@/components/exerciseLibrary/prescriptionModal/preSessionMessages';
import VoiceRecording from '@/components/exerciseLibrary/prescriptionModal/voiceMessage/voiceRecording';
import TextToSpeech from '@/components/exerciseLibrary/prescriptionModal/voiceMessage/textToSpeech';
import PreSessionMessagesModal from '@/components/exerciseLibrary/PreSessionMoal';
import ReplaceIcon from '@/assets/svgs/exercise-library/replcae.svg';
import ActionModal from '@/components/actionModal';

const contentCar = [
  {
    id: 0,
    title: 'Arm Circles',
    isFavorite: true,
    imgUrl: Exerciseimg3,
    videoUrl:
      'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
    reps: 10,
    sets: 4,
  },
  {
    id: 1,
    title: 'Cat Cow Strech',
    isFavorite: false,
    imgUrl: Exerciseimg3,
    reps: 10,
    sets: 4,
    videoUrl:
      'https://videocdn.cdnpk.net/videos/653caf65-0e8e-4941-b3d9-798a28ecfe6a/horizontal/previews/clear/small.mp4?token=exp=1750231577~hmac=120e287183a1ff237634fd6f7960264e6e3bb9c750bf04c0c37194009a4b49e8',
  },
  {
    id: 2,
    title: 'Ankle Circles',
    isFavorite: false,
    imgUrl: Exerciseimg3,
    reps: 10,
    sets: 4,
    videoUrl:
      'https://videocdn.cdnpk.net/videos/72d0d679-cb78-4bf7-8fa1-ffbbbb06764c/horizontal/previews/clear/small.mp4?token=exp=1750231577~hmac=d0db94af97427f46e519ca9a3810281b073eadf5967b6b940d7c0af38b87f510',
  },
  {
    id: 3,
    title: 'Arm Pendulum Exercise',
    isFavorite: false,
    imgUrl: Exerciseimg3,
    reps: 10,
    sets: 4,
    videoUrl:
      'https://videocdn.cdnpk.net/videos/ef442b2d-126a-5bf2-b30e-0508452826f1/horizontal/previews/watermarked/small.mp4',
  },
  {
    id: 4,
    title: 'Doorway Stretch',
    isFavorite: false,
    imgUrl: Exerciseimg3,
    reps: 5,
    sets: 23,
    videoUrl:
      'https://videocdn.cdnpk.net/videos/809c1834-bcc5-4ded-90e2-989b05e28203/horizontal/previews/clear/small.mp4?token=exp=1750231577~hmac=029e2ca5f23a6294e93818e67f01b302820f24e2266f2c86d964b7c40e568fbc',
  },
  {
    id: 5,
    title: 'Chair Cat-Cow Pose',
    isFavorite: false,
    reps: 20,
    sets: 4,
    imgUrl: Exerciseimg3,
    videoUrl:
      'https://videocdn.cdnpk.net/videos/53241399-d2e4-45f9-bef3-f201520fca1d/horizontal/previews/clear/small.mp4?token=exp=1750231577~hmac=26b609a3a7908e7586aeba0d7f3223d432e5b94b0592f41fe1198b2cca9d3be0',
  },
  {
    id: 6,
    title: 'Kittle bell Arm Windmills',
    isFavorite: false,
    imgUrl: Exerciseimg3,
    reps: 10,
    sets: 56,
    videoUrl:
      'https://videocdn.cdnpk.net/videos/e482d99f-558f-540a-92d6-08b6eee516d1/horizontal/previews/clear/small.mp4?token=exp=1750231577~hmac=6391ddb74684c9b6cf612b6dad78cb7f7944bbb482d41d787eb51692392a074f',
  },
  {
    id: 7,
    title: 'Knee-To-Chest Stretch',
    isFavorite: false,
    reps: 8,
    sets: 3,
    imgUrl: Exerciseimg3,
    videoUrl: 'https://assets.mixkit.co/videos/40881/40881-720.mp4',
  },
];

// List of path IDs to color
const PATH_IDS = [
  { id: 'left_foot', color: '#8DA12B' },
  { id: 'right_foot', color: '#6A7920' },
  { id: 'left_ankle', color: '#C0D45E' },
  { id: 'right_ankle', color: '#C6D86E' },
  { id: 'right_leg', color: '#475016' },
  { id: 'left_leg', color: '' },
  { id: 'right_thigh', color: '' },
  { id: 'left_thigh', color: '' },
  { id: 'left_knee', color: '' },
  { id: 'left_hip', color: '' },
  { id: 'right_hand', color: '#6A7920' },
  { id: 'left_hand', color: '' },
  { id: 'right_wrist', color: '' },
  { id: 'left_wrist', color: '' },
  { id: 'left_arm', color: '#C0D45E' },
  { id: 'right_forearm', color: '' },
  { id: 'left_forearm', color: '' },
  { id: 'left_elbow', color: '#6A7920' },
  { id: 'stomach', color: '' },
  { id: 'back', color: '' },
  { id: 'chest', color: '' },
  { id: 'left_shoulder', color: '' },
  { id: 'right_shoulder', color: '#6A7920' },
  { id: 'neck', color: '' },
  { id: 'header', color: '' },
];

const PATH_IDS_2 = [
  { id: 'left_foot', color: '' },
  { id: 'right_foot', color: '' },
  { id: 'left_ankle', color: '' },
  { id: 'right_ankle', color: '#C6D86E' },
  { id: 'right_leg', color: '' },
  { id: 'left_leg', color: '' },
  { id: 'right_thigh', color: '' },
  { id: 'left_thigh', color: '#6A7920' },
  { id: 'left_knee', color: '' },
  { id: 'left_hip', color: '' },
  { id: 'right_hand', color: '#6A7920' },
  { id: 'left_hand', color: '' },
  { id: 'right_wrist', color: '#6A7920' },
  { id: 'left_wrist', color: '' },
  { id: 'left_arm', color: '#C0D45E' },
  { id: 'right_forearm', color: '' },
  { id: 'left_forearm', color: '#8DA12B' },
  { id: 'left_elbow', color: '' },
  { id: 'stomach', color: '#475016' },
  { id: 'back', color: '' },
  { id: 'chest', color: '#C0D45E' },
  { id: 'left_shoulder', color: '' },
  { id: 'right_shoulder', color: '#6A7920' },
  { id: 'neck', color: '#6A7920' },
  { id: 'header', color: '' },
];

const templateExample = [
  {
    id: 7,
    title: 'Whole body stiffness relief',
    isFavorite: true,
    imgUrl: Exerciseimg3,
    exercises: contentCar.slice(0, 2),
    PATH_IDS: PATH_IDS,
    frequency: { day: 2, week: 2 },
    duration: 20,
    description: 'one tow three',
    isDefault: true,
  },
  {
    id: 8,
    title: 'sl,dfl',
    isFavorite: true,
    imgUrl: Exerciseimg3,
    exercises: contentCar.slice(3, 4),
    PATH_IDS: PATH_IDS_2,
    frequency: { day: 4, week: 5 },
    duration: 20,
    description: 'lorem iasdadasdadsadadasdsadsa',
  },
];

const ExerciseLibrary = () => {
  const [selectedCard, setSelectedCard] = useState([]);
  const [checked, setChecked] = useState(false);
  const [openExercisesDetailsModel, setOpenExerciseDetailsModel] = useState(false);
  const [currentExercise, setCurrentExercise] = useState(null);
  const [exerciseStack, setExerciseStack] = useState([]);
  const [modalView, setModalView] = useState(ModalViews.PRE_SESSION_MESSAGES);
  const [removeId, setRemoveId] = useState('');

  const [voiceField, setVoiceField] = useState('');
  const {
    setEditTemplateData,
    addEditTemplateData,
    EditTemplateData,
    openNotesView,
    setOpenNotesView,
    mode,
  } = useExerciseLibrary();

  const [state, dispatch] = useReducer(exerciseLibraryReducer, initialValues);
  const navigate = useNavigate();
  const location = useLocation();
  let prescription_id = location.state;

  // Check for pending navigation from sidebar
  useEffect(() => {
    const pendingPath = localStorage.getItem('pendingPath');
    const isEdit = localStorage.getItem('isEdit');

    if (pendingPath && isEdit === 'false') {
      // User attempted to navigate from sidebar while editing
      dispatch({ type: exerciseLibraryActions.cancelFilter, payload: true });
      dispatch({ type: exerciseLibraryActions.setPendingPath, payload: pendingPath });
      localStorage.removeItem('pendingPath');
    }
  }, []);

  // Listen for navigation modal event from sidebar
  useEffect(() => {
    const handleNavigationModal = event => {
      const { path } = event.detail;
      // Ensure modal opens by setting the state
      dispatch({ type: exerciseLibraryActions.cancelFilter, payload: true });
      dispatch({ type: exerciseLibraryActions.setPendingPath, payload: path });
    };

    window.addEventListener('showNavigationModal', handleNavigationModal);

    return () => {
      window.removeEventListener('showNavigationModal', handleNavigationModal);
    };
  }, [dispatch]);

  const {
    register,
    handleSubmit,
    setValue,
    getValues,
    trigger,
    watch,
    control,
    reset,
    formState: { errors, isValid, touchedFields },
  } = useForm({
    defaultValues: InitialState,
    mode: 'onChange',
    resolver: yupResolver(schemaPrescriptionForm),
  });

  useEffect(() => {
    if (prescription_id && mode === prescriptionMode.EDIT) {
      const prescription = getPrescriptionById(prescription_id);
      const mappedPerscsription = mapPrescriptionToExerciseLibraryView(prescription);
      const updatedState = {
        search: '',
        frequency_day: mappedPerscsription.frequency_day,
        frequency_week: mappedPerscsription.frequency_week,
        duration: mappedPerscsription.duration,
        prescription_name: mappedPerscsription.prescription_name,
        patients: mappedPerscsription.patients,
        startDate: mappedPerscsription.startDate,
        generalMessage: mappedPerscsription.generalMessage,
        exerciseMessages: mappedPerscsription.exerciseMessages,
      };
      dispatch({
        type: exerciseLibraryActions.prescriptionTabArray,
        payload: [
          { id: 0, text: 'prescription' },
          { id: 1, text: mappedPerscsription.prescription_name },
        ],
      });
      dispatch({ type: exerciseLibraryActions.activePrescriptionTab, payload: 1 });

      reset(updatedState);
    } else {
      reset({
        ...getValues(), // Preserve the current form state
        frequency_day: 1, // Update only frequency_day
        frequency_week: 1, // Update only frequency_week
        duration: 1, // Update only duration
      });
    }

    //will be removed on production
    prescription_id = null;
  }, []);

  const [searchParams, setSearchParams] = useSearchParams();
  const duplicateCounter = useRef(0);
  const sectionRef = useRef();

  const { width80 } = useCalculatePrescriptionWidth({ sectionRef });

  const handleDelete = itemId => {
    if (state.activePrescriptionTab === 0) {
      const filterItem = selectedCard.filter(item => item.id !== itemId);
      setSelectedCard(filterItem);
      const newFirstTemplateIds = state.firstTemplateIds.filter(templateId => {
        const template = templateExample.find(t => t.id === templateId);
        if (!template) return false;
        return template.exercises.every(exercise => {
          const baseId = exercise.id.toString();
          return filterItem.some(e => e.id === baseId || e.id.includes(`target-id-${baseId}`));
        });
      });
      dispatch({ type: exerciseLibraryActions.setFirstTemplateIds, payload: newFirstTemplateIds });
    } else {
      const filterItem = EditTemplateData.filter(item => item.id !== itemId);
      setEditTemplateData(filterItem);
      const newFirstTemplateIds = state.firstTemplateIds.filter(templateId => {
        const template = templateExample.find(t => t.id === templateId);
        if (!template) return false;
        return template.exercises.every(exercise => {
          const baseId = exercise.id.toString();
          return filterItem.some(e => e.id === baseId || e.id.includes(`target-id-${baseId}`));
        });
      });
      dispatch({ type: exerciseLibraryActions.setFirstTemplateIds, payload: newFirstTemplateIds });
    }
  };

  const handleDeleteTemplate = template => {
    if (!template || !template.exercises) {
      return;
    }

    dispatch({
      type: exerciseLibraryActions.setFirstTemplateIds,
      payload: state.firstTemplateIds.filter(
        e => !(e.tab === state.activePrescriptionTab && e.id === template.id)
      ),
    });

    if (state.activePrescriptionTab === 0) {
      const updatedSelectedCard = [...selectedCard];

      template.exercises.forEach(templateExercise => {
        const baseId = templateExercise.id.toString();

        const duplicates = updatedSelectedCard
          .filter(e => e.id.includes(`target-id-${baseId}`))
          .sort((a, b) => {
            const aTime = b.createdAt ?? 0;
            const bTime = a.createdAt ?? 0;
            return aTime - bTime; // Newest first
          });

        if (duplicates.length > 0) {
          const index = updatedSelectedCard.findIndex(e => e.id === duplicates[0].id);
          if (index !== -1) {
            updatedSelectedCard.splice(index, 1);
          }
        } else {
          const index = updatedSelectedCard.findIndex(
            e => e.id === baseId || e.id === `target-id-${baseId}`
          );
          if (index !== -1) {
            updatedSelectedCard.splice(index, 1);
          }
        }
      });

      const stillAllExist = template.exercises.every(templateExercise => {
        const baseId = templateExercise.id.toString();
        return updatedSelectedCard.some(
          e => e.id === baseId || e.id.includes(`target-id-${baseId}`)
        );
      });
      if (!stillAllExist && state.firstTemplateIds.includes(template.id)) {
        dispatch({
          type: exerciseLibraryActions.setFirstTemplateIds,
          payload: state.firstTemplateIds.filter(
            e => !(e.tab === state.activePrescriptionTab && e.id === template.id)
          ),
        });
      }

      const hasMoreDuplicates = updatedSelectedCard.some(e =>
        template.exercises.some(te => e.id.includes(`target-id-${te.id}`))
      );

      if (
        state.firstTemplateIds.includes(template.id) &&
        !template.isDuplicate &&
        !hasMoreDuplicates
      ) {
        setValue('frequency_day', 1);
        setValue('frequency_week', 1);
        setValue('duration', 1);
        dispatch({
          type: exerciseLibraryActions.setFirstTemplateIds,
          payload: state.firstTemplateIds.filter(
            e => !(e.tab === state.activePrescriptionTab && e.id === template.id)
          ),
        });
      }
      setSelectedCard(updatedSelectedCard);
    } else {
      const updatedEditTemplateData = [...EditTemplateData];

      template.exercises.forEach(templateExercise => {
        const baseId = templateExercise.id.toString();

        const duplicates = updatedEditTemplateData
          .filter(e => e.id.includes(`target-id-${baseId}`))
          .sort((a, b) => {
            const aTime = b.createdAt ?? 0;
            const bTime = a.createdAt ?? 0;
            return aTime - bTime; // Newest first
          });

        if (duplicates.length > 0) {
          const index = updatedEditTemplateData.findIndex(e => e.id === duplicates[0].id);
          if (index !== -1) {
            updatedEditTemplateData.splice(index, 1);
          }
        } else {
          const index = updatedEditTemplateData.findIndex(
            e => e.id === baseId || e.id === `target-id-${baseId}`
          );
          if (index !== -1) {
            updatedEditTemplateData.splice(index, 1);
          }
        }
      });

      setEditTemplateData(updatedEditTemplateData);
    }
  };

  const handleDuplicate = item => {
    if (item.exercises) {
      const now = Date.now();
      const duplicatedExercises = item.exercises.map(exercise => ({
        ...exercise,
        id: `target-id-${exercise.id}-${duplicateCounter.current}`,
        isDuplicate: true,
        createdAt: now + Math.random(),
      }));
      if (state.activePrescriptionTab === 0) {
        setSelectedCard(prev => [...prev, ...duplicatedExercises]);
        if (item.id && !state.firstTemplateIds.includes(item.id)) {
          dispatch({
            type: exerciseLibraryActions.setFirstTemplateIds,
            payload: [...state.firstTemplateIds, item.id],
          });
        }
      } else {
        addEditTemplateData(duplicatedExercises);
        if (item.id && !state.firstTemplateIds.includes(item.id)) {
          dispatch({
            type: exerciseLibraryActions.setFirstTemplateIds,
            payload: [...state.firstTemplateIds, item.id],
          });
        }
      }
      duplicateCounter.current = duplicateCounter.current + 1;
    } else {
      const newItem = {
        ...item,
        id: `target-id-${item.id}-${duplicateCounter.current}`,
        isDuplicate: true,
      };
      if (state.activePrescriptionTab === 0) {
        setSelectedCard(prev => [...prev, newItem]);
      } else {
        addEditTemplateData([newItem]);
      }
      duplicateCounter.current = duplicateCounter.current + 1;
    }
  };

  const handleFavouriteClick = () => {
    setChecked(!checked);
  };

  // Get current tab index based on URL query parameter
  const getCurrentTabIndex = () => {
    const tabParam = searchParams.get('tab');
    if (tabParam === 'templates') {
      return 1;
    }
    return 0; // Default to exercises
  };

  const handelExerciseClick = exercise => {
    setCurrentExercise(exercise);
    setExerciseStack([]); // reset stack when opening from main list
    setOpenExerciseDetailsModel(true);
  };

  const handleRelatedExerciseClick = exercise => {
    setExerciseStack(prev => [...prev, currentExercise]);
    setCurrentExercise(exercise);
    setOpenExerciseDetailsModel(true);
  };

  const handleBackToPreviousExercise = () => {
    setExerciseStack(prev => {
      if (prev.length === 0) return prev;
      const newStack = [...prev];
      const lastExercise = newStack.pop();
      setCurrentExercise(lastExercise);
      return newStack;
    });
    setOpenExerciseDetailsModel(true);
  };

  const handelCardClick = item => {
    if (!state.isEditFilter) {
      handelExerciseClick(item);
    } else {
      dispatch({
        type: exerciseLibraryActions.toggleFilterItem,
        payload: item.id,
      });
    }
  };

  const handelTemplateCardClick = item => {
    dispatch({ type: exerciseLibraryActions.openTemplateModel, payload: true });
    dispatch({ type: exerciseLibraryActions.selectedTemplateId, payload: item });
  };
  const handelTemplateModelClose = () => {
    dispatch({ type: exerciseLibraryActions.openTemplateModel, payload: false });
    dispatch({ type: exerciseLibraryActions.selectedTemplateId, payload: null });
  };

  const handleAddAllTemplateExercises = (exercises, template) => {
    const newExercises = exercises.map(exercise => ({
      ...exercise,
      id: `target-id-${exercise.id}-${Date.now()}-${Math.random()}`,
      isDuplicate: false,
      fromTemplate: true,
      templateId: template.id,
    }));

    // if (state.activePrescriptionTab === 0) {
    setSelectedCard(prev => {
      const updated = [...prev, ...newExercises];
      if (template) {
        const ids = state.firstTemplateIds;
        if (!ids.some(e => e.tab === state.activePrescriptionTab && e.id === template.id)) {
          dispatch({
            type: exerciseLibraryActions.setFirstTemplateIds,
            payload: [...ids, { tab: state.activePrescriptionTab, id: template.id }],
          });
        }
      }
      return updated;
    });
  };

  const handleAddSelectedExercises = (selectedExercises, templateId) => {
    const newExercises = selectedExercises.map(exercise => ({
      ...exercise,
      id: `target-id-${exercise.id}-${Date.now()}-${Math.random()}`,
      isDuplicate: false,
      fromTemplate: true,
      templateId: templateId,
    }));
    setSelectedCard(prev => [...prev, ...newExercises]);
  };

  const tabsData = [
    {
      id: 0,
      title: 'Exercises',
      component: (
        <div tw="grid md:grid-cols-3 grid-cols-2 lg:grid-cols-5 4xl:grid-cols-7 3xl:grid-cols-6 2xl:grid-cols-6 xl:grid-cols-6 gap-[20px]">
          {contentCar.map(item => (
            <SourceItem
              item={item}
              key={item.id}
              handleDelete={handleDelete}
              handelCardClick={() => handelCardClick(item)}
              handleDuplicate={handleDuplicate}
              handleFavouriteClick={handleFavouriteClick}
              selectedCard={state.activePrescriptionTab === 0 ? selectedCard : EditTemplateData}
              setSelectedCard={
                state.activePrescriptionTab === 0 ? setSelectedCard : setEditTemplateData
              }
              showActions={!state.isEditFilter}
              canDrag={!state.isEditFilter}
              state={state}
            />
          ))}
        </div>
      ),
    },
    {
      id: 1,
      title: 'Templates',
      component: (
        <div tw="grid md:grid-cols-3 grid-cols-2 lg:grid-cols-5 4xl:grid-cols-7 3xl:grid-cols-6 2xl:grid-cols-6 xl:grid-cols-6 gap-[20px]">
          {templateExample.map(item => (
            <SourceItemTemplate
              item={item}
              key={item.id}
              handleDelete={handleDeleteTemplate}
              handleDuplicate={handleDuplicate}
              handleFavouriteClick={handleFavouriteClick}
              selectedCard={state.activePrescriptionTab === 0 ? selectedCard : EditTemplateData}
              PathIds={item.PATH_IDS}
              handelTemplateCardClick={() => {
                handelTemplateCardClick(item);
                dispatch({ type: exerciseLibraryActions.selectedTemplateToDelete, payload: item });
              }}
              handleAddTemplate={handleAddAllTemplateExercises}
              firstTemplateIds={state.firstTemplateIds}
              activeTab={state.activePrescriptionTab}
              dispatch={dispatch}
              removeId={removeId}
              setSelectedCard={setSelectedCard}
              setValue={setValue}
              templates={templateExample}
            />
          ))}
        </div>
      ),
    },
  ];

  const handelCloseSaveTemplateModal = () => {
    dispatch({ type: exerciseLibraryActions.openSaveTemplateModal, payload: false });
  };

  const handleClosePrescriptionModal = () => {
    dispatch({ type: exerciseLibraryActions.openPrescriptionModal, payload: false });
  };

  const handleOpenPrescriptionModal = () => {
    dispatch({ type: exerciseLibraryActions.openPrescriptionModal, payload: true });
  };

  const handelCloseModel = () => {
    dispatch({ type: exerciseLibraryActions.cancelFilter, payload: false });
    dispatch({ type: exerciseLibraryActions.setPendingPath, payload: '' });
    localStorage.removeItem('isEdit');
    localStorage.removeItem('pendingPath');
    dispatch({
      type: exerciseLibraryActions.selectedItemToDelete,
      payload: null,
    });
    dispatch({
      type: exerciseLibraryActions.opendDeleteModal,
      payload: false,
    });
    dispatch({
      type: exerciseLibraryActions.openDeleteTemplateModal,
      payload: false,
    });
    dispatch({
      type: exerciseLibraryActions.selectedItemToDelete,
      payload: null,
    });
  };

  const resetPrescriptionForm = () => {
    setValue('frequency_day', 1);
    setValue('frequency_week', 1);
    setValue('duration', 1);
  };

  useEffect(() => {
    if (selectedCard?.length === 0) {
      resetPrescriptionForm();
    }
  }, [selectedCard]);

  const handelDelete = () => {
    dispatch({
      type: exerciseLibraryActions.deleteCustomFilter,
      payload: state?.selectedItemToDelete?.id,
    });
    dispatch({
      type: exerciseLibraryActions.opendDeleteModal,
      payload: false,
    });
  };

  const handelDeleteFilter = item => {
    dispatch({
      type: exerciseLibraryActions.selectedItemToDelete,
      payload: item,
    });
    dispatch({
      type: exerciseLibraryActions.opendDeleteModal,
      payload: true,
    });
  };

  const selectedTemplate = selectedCard.find(item => item.templateId);
  const selectedTemplateName = templateExample.find(
    item => item.id === selectedTemplate?.templateId
  )?.title;

  const handelDeleteTemplate = () => {
    console.log('click', state.selectedTemplateToDelete);
    dispatch({
      type: exerciseLibraryActions.openDeleteTemplateModal,
      payload: false,
    });
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <section
        tw="bg-white pl-[24px] pr-[24px] h-full flex flex-col gap-[12px]"
        className="element"
        ref={sectionRef}
      >
        <div tw="flex sticky top-0 left-0 flex-col gap-4 h-auto bg-white">
          <PrescriptionCard
            exercises={contentCar}
            templates={templateExample}
            duplicateCounter={duplicateCounter}
            handleDelete={handleDelete}
            register={register}
            selectedCard={selectedCard}
            setSelectedCard={setSelectedCard}
            width80={width80}
            setValue={setValue}
            getCurrentTabIndex={getCurrentTabIndex}
            state={state}
            dispatch={dispatch}
            errors={errors}
            touchedFields={touchedFields}
            watch={watch}
            handleSettings={item => handelCardClick(item)}
            handleOpenPrescriptionModal={handleOpenPrescriptionModal}
            setRemoveId={setRemoveId}
            resetPrescriptionForm={resetPrescriptionForm}
          />
        </div>
        <TabsPage
          register={register}
          setSearchParams={setSearchParams}
          searchParams={searchParams}
          tabsData={tabsData}
          state={state}
          getCurrentTabIndex={getCurrentTabIndex}
        />
        <div
          tw="flex items-start relative gap-[24px] h-auto overflow-y-auto pb-[10px]"
          className="element"
        >
          <Filter
            register={register}
            watch={watch}
            control={control}
            setValue={setValue}
            dispatch={dispatch}
            state={state}
            setSearchParams={setSearchParams}
            onDelete={handelDeleteFilter}
          />
          <div tw="w-[85%]" id="exercise-list">
            {state.isEditFilter && (
              <div tw="flex sticky top-0 left-0 justify-between items-center z-[10] bg-white">
                <p>
                  {state.editFilterId > 0
                    ? 'Select exercises to add to or remove from your list'
                    : 'Select exercises to add to your list'}
                </p>
                <div tw="flex gap-4 items-center mb-4">
                  <PrimaryButton
                    text={state.editFilterId > 0 ? 'Save changes' : 'Create list'}
                    customStyle={tw`h-full py-[8px] rounded-[8px]!`}
                    disable={state.selectedFilteredItem?.length === 0}
                    handleClick={() => {
                      if (!watch('filter_custom_name')) {
                        showError('add filter name before save');
                        return;
                      }

                      dispatch({
                        type: exerciseLibraryActions.openCollapseModel,
                        payload: true,
                      });
                      if (state.editFilterId > 0) {
                        dispatch({
                          type: exerciseLibraryActions.updateCustomFilter,
                          payload: {
                            id: state.editFilterId,
                            name: watch('filter_custom_name'),
                            ids: state.selectedFilteredItem,
                          },
                        });
                        dispatch({ type: exerciseLibraryActions.isEditFilter, payload: false });
                        showSuccess('Filter updated successfully');
                        setValue('filter_custom_name', '');
                      } else {
                        dispatch({
                          type: exerciseLibraryActions.addCustomFilter,
                          payload: {
                            name: watch('filter_custom_name'),
                            ids: state.selectedFilteredItem,
                          },
                        });
                        showSuccess('Filter created successfully');
                        dispatch({ type: exerciseLibraryActions.isEditFilter, payload: false });
                        setValue('filter_custom_name', '');
                        localStorage.removeItem('isEdit');
                        localStorage.removeItem('pendingPath');
                      }
                    }}
                  />
                  <SecondaryButton
                    text="Cancel"
                    handelClick={() => {
                      dispatch({
                        type: exerciseLibraryActions.cancelFilter,
                        payload: true,
                      });
                    }}
                    otherStyle={tw`py-[7px] h-full w-fit px-4 rounded-[8px]!`}
                  />
                </div>
              </div>
            )}

            {tabsData[getCurrentTabIndex()].component}
          </div>
        </div>
      </section>

      {openExercisesDetailsModel && (
        <ExerciseDetailsModel
          exercise={currentExercise}
          PrimaryButtonText={'Add to prescription'}
          clickOnPrimaryButton={item => {
            handleAddSelectedExercises([item]);
            setOpenExerciseDetailsModel(false);
          }}
          exerciseList={contentCar}
          openModel={openExercisesDetailsModel}
          errors={errors}
          handelCloseMode={() => setOpenExerciseDetailsModel(false)}
          control={control}
          register={register}
          setValue={setValue}
          secondaryButtonText={'Play demo'}
          onRelatedExerciseClick={handleRelatedExerciseClick}
          onBackToPreviousExercise={handleBackToPreviousExercise}
          exerciseStack={exerciseStack}
          handleDelete={handleDelete}
          handleFavouriteClick={handleFavouriteClick}
          selectedCard={selectedCard}
          setSelectedCard={setSelectedCard}
          handleDuplicate={handleDuplicate}
        />
      )}
      {state.openTemplateModel && (
        <EditTemplateModel
          openModel={state.openTemplateModel}
          handelCloseMode={handelTemplateModelClose}
          exerciseList={state?.selectedTemplateId?.exercises}
          template={state?.selectedTemplateId}
          setValue={setValue}
          setSelectedCard={setSelectedCard}
          setFirstTemplateIds={dispatch}
          handleAddSelectedExercises={handleAddSelectedExercises}
          dispatch={dispatch}
          selectedCard={selectedCard}
          isDefault={state.selectedTemplateToDelete.isDefault}
        />
      )}
      {state.openSaveTemplateModal && (
        <SaveTemplateModal
          handelCloseMode={handelCloseSaveTemplateModal}
          open={state.openSaveTemplateModal}
          setSelectedCard={setSelectedCard}
          templateName={selectedTemplateName}
          dispatch={dispatch}
        />
      )}
      {state.openPrescriptionModal && (
        <PrescriptionModal
          open={state.openPrescriptionModal}
          handleClose={handleClosePrescriptionModal}
          exercises={selectedCard}
          register={register}
          handleSubmit={handleSubmit}
          control={control}
          watch={watch}
          setValue={setValue}
          getValues={getValues}
          trigger={trigger}
          errors={errors}
          isValid={isValid}
          reset={reset}
          setSelectedCard={setSelectedCard}
        />
      )}
      {openNotesView && (
        <PreSessionMessagesModal
          open={openNotesView}
          handleClose={() => setOpenNotesView(false)}
          modalView={modalView}
        >
          {modalView === ModalViews.PRE_SESSION_MESSAGES && (
            <PreSessionMessages
              state={getValues('exerciseMessages')}
              register={register}
              errors={errors}
              setModalView={setModalView}
              setVoiceField={setVoiceField}
              watch={watch}
              setValue={setValue}
              exercises={EditTemplateData}
            />
          )}
          {modalView === ModalViews.VOICE_RECORDING && (
            <VoiceRecording setModalView={setModalView} setValue={setValue} field={voiceField} />
          )}
          {modalView === ModalViews.TEXT_TO_SPEECH && (
            <TextToSpeech setModalView={setModalView} setValue={setValue} field={voiceField} />
          )}
        </PreSessionMessagesModal>
      )}
      {state.cancelFilter && (
        <ActionModal
          open={state.cancelFilter}
          handleClose={handelCloseModel}
          primaryActionHandler={() => {
            dispatch({
              type: exerciseLibraryActions.openCollapseModel,
              payload: true,
            });
            dispatch({ type: exerciseLibraryActions.deleteCustomFilter });
            dispatch({ type: exerciseLibraryActions.cancelFilter, payload: false });
            dispatch({ type: exerciseLibraryActions.isEditFilter, payload: false });
            localStorage.removeItem('isEdit');
            localStorage.removeItem('pendingPath');

            // Navigate to pending path if exists
            if (state.setPendingPath && state.setPendingPath !== '') {
              navigate(state.setPendingPath);
            }
          }}
          title="Unsaved Changes Detected"
          cancelButtonText="Cancel"
          customIcon={cancelSubscriptionModal}
          actionButtonText="Leave"
          description="You have unsaved custom lists. If you leave this page now, any unsaved changes will be lost."
        />
      )}
      {state.opendDeleteModal && (
        <ActionModal
          open={state.opendDeleteModal}
          handleClose={handelCloseModel}
          primaryActionHandler={handelDelete}
          title={`Delete Custom List ${state.selectedItemToDelete?.title}`}
          cancelButtonText="Cancel"
          actionButtonText="Delete"
          description={`Are you sure you want to delete the ${state.selectedItemToDelete?.title}? This action cannot be undone.`}
        />
      )}
      {state.openReplaceTemplateModal && (
        <ActionModal
          open={state.openReplaceTemplateModal}
          handleClose={handelCloseModel}
          primaryActionHandler={handelDelete}
          title={'Replace template'}
          customIcon={ReplaceIcon}
          cancelButtonText="No"
          customHeaderStyle={tw`border-[#FFF6E9] bg-[#FFEACC]`}
          actionButtonText="Yes"
          PrimaryButtonStyle={tw`border-stroke bg-white text-text_primary hover:!bg-none`}
          description={`${selectedTemplateName} already exists. Do you want to replace it?`}
        />
      )}
      {state.openDeleteTemplateModal && (
        <ActionModal
          open={state.openDeleteTemplateModal}
          handleClose={handelCloseModel}
          primaryActionHandler={handelDeleteTemplate}
          title={`Delete template ${state.selectedTemplateToDelete?.title}`}
          cancelButtonText="Cancel"
          actionButtonText="Delete"
          description={`Are you sure you want to delete the ${state.selectedTemplateToDelete?.title}? This action cannot be undone.`}
        />
      )}
    </DndProvider>
  );
};

export default ExerciseLibrary;
