import GenericModal from '@/components/genericModal';
import ExerciseDetailsModelHeader from '../exerciseDetailsModelHeader';
import ExerciseDetailsModelTags from '../exerciseDetailsModelTags';
import { useForm } from 'react-hook-form';
import Input from '@/components/shared/input';
import TextArea from '@/components/shared/textarea';
import { showSuccess } from '@/libs/react.toastify';
import LabelWithTooltip from '@/components/labelWithTooltip';
import { exerciseLibraryActions } from '@/reducers/exercise-library';
import { useNavigate } from 'react-router-dom';
import tw from 'twin.macro';

const SaveTemplateModal = ({
  handelCloseMode,
  open,
  dispatch,
  setSelectedCard,
  navigateTo,
  templateName,
}) => {
  const { register, reset, handleSubmit } = useForm({
    defaultValues: { name: templateName, descriptions: '' },
  });
  const navigate = useNavigate();
  const Tags = [
    { id: 0, title: 'Region: Chest' },
    { id: 1, title: 'Region: Back' },
    { id: 2, title: 'Region: Knees' },
    { id: 3, title: 'Joints: Hips' },
    { id: 4, title: 'Region: Chest' },
  ];

  const handelCLoseModal = () => {
    reset();
    setSelectedCard?.([]);
    dispatch?.({ type: exerciseLibraryActions.resetPrescription });
  };

  const onSubmit = async data => {
    try {
      // console.log('data', data);
      if (templateName === data.name) {
        dispatch({ type: exerciseLibraryActions.openReplaceTemplateModal, payload: true });
        return;
      } else {
        showSuccess('saved template');
        handelCLoseModal();
      }
    } catch (error) {
      console.log('error', error);
    }
  };
  return (
    <GenericModal
      PrimaryButtonStyle={tw`basis-[50%] rounded-[6px]`}
      SecondaryButtonStyle={tw`basis-[50%] rounded-[6px]`}
      backgroundModal={'rgba(0,0,0,0.4)'}
      handelCloseMode={handelCloseMode}
      handelFormSubmit={handleSubmit(onSubmit)}
      modalStyle={tw`backdrop-blur-[0px]`}
      containerModalStyle={tw`rounded-card! w-[40vw]`}
      PrimaryButtonText={'Save & Continue'}
      secondaryButtonText={'Cancel'}
      openModel={open}
      element="form"
      typeOfPrimaryButton={'submit'}
      typeOfSecondaryButton={'button'}
      clickOnPrimaryButton={() => {
        if (navigateTo) navigate(navigateTo);
        handelCloseMode();
      }}
      clickOnSecondaryButton={() => {
        handelCloseMode();
      }}
      contetnContainerStyle={tw`py-4`}
      containerButtonsStyle={tw`p-5!`}
      content={
        <div
          tw="grid gap-4 px-5 max-h-[65vh] overflow-y-auto"
          id="scrollable-section"
          className="element"
        >
          <Input
            register={register}
            name={'name'}
            showArrow={false}
            placeholder={'Template 1'}
            labelStyle={tw`text-text_secondary font-normal mb-2`}
            label={'Template name (required):'}
          />
          <TextArea
            label={'Template description (optional):'}
            name={'description'}
            labelStyle={tw`text-text_secondary font-normal mb-2`}
            register={register}
            rows={6}
            textAreaStye={tw`rounded-[6px] border border-border_stroke p-[12px] focus:(border border-Primary_600)`}
            containerSTyle={tw`p-0 border-none hover:bg-neutral-50 bg-white duration-300 transition-all`}
            placeholder={''}
          />
          <div>
            <LabelWithTooltip
              label="Categories (required):"
              labelStyle={tw`text-text_secondary font-normal mb-1`}
            />
            <ExerciseDetailsModelTags
              hideName
              hiedAddTags={false}
              tagsList={Tags}
              showCloseIcon
              containerStyle={tw`border-none py-0!`}
            />
          </div>
        </div>
      }
      title={
        <ExerciseDetailsModelHeader
          handelCloseClick={handelCloseMode}
          title="Save template"
          hideArrow
          containerStyle={tw`bg-neutral-50`}
        />
      }
    />
  );
};

export default SaveTemplateModal;
